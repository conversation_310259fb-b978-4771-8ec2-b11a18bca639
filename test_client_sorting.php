<?php
// test_client_sorting.php - Test client table sorting functionality

session_start();
require_once 'config.php';

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h1>Client Table Sorting Test</h1>";

// Test 1: Check if we have client data to sort
echo "<h2>1. Client Data Check</h2>";

try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM clients");
    $result = $stmt->fetch();
    $totalClients = $result['total'];
    
    if ($totalClients > 0) {
        echo "✅ Found $totalClients clients in database<br>";
        
        // Show sample data for each sortable column
        echo "<h3>Sample Data Preview:</h3>";
        $stmt = $pdo->query("SELECT client_id, name, ic_number, gender FROM clients LIMIT 5");
        $samples = $stmt->fetchAll();
        
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Client ID</th><th>Name</th><th>IC Number</th><th>Gender</th>";
        echo "</tr>";
        
        foreach ($samples as $sample) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($sample['client_id']) . "</td>";
            echo "<td>" . htmlspecialchars($sample['name']) . "</td>";
            echo "<td>" . htmlspecialchars($sample['ic_number'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($sample['gender'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "❌ No clients found in database<br>";
        echo "<p>You need to add some clients first to test sorting functionality.</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking client data: " . $e->getMessage() . "<br>";
}

// Test 2: Test sorting functionality
echo "<h2>2. Sorting Functionality Test</h2>";

if ($totalClients > 0) {
    $sortTests = [
        ['column' => 'client_id', 'order' => 'ASC', 'title' => 'Client ID (Ascending)'],
        ['column' => 'client_id', 'order' => 'DESC', 'title' => 'Client ID (Descending)'],
        ['column' => 'name', 'order' => 'ASC', 'title' => 'Name (A-Z)'],
        ['column' => 'name', 'order' => 'DESC', 'title' => 'Name (Z-A)'],
        ['column' => 'ic_number', 'order' => 'ASC', 'title' => 'IC Number (Ascending)'],
        ['column' => 'gender', 'order' => 'ASC', 'title' => 'Gender (Ascending)']
    ];
    
    echo "<h3>Sorting Test Links:</h3>";
    echo "<p>Click these links to test different sorting options:</p>";
    echo "<ul>";
    
    foreach ($sortTests as $test) {
        $url = "clients.php?sort=" . $test['column'] . "&order=" . $test['order'];
        echo "<li><a href='$url' target='_blank'>" . $test['title'] . "</a></li>";
    }
    
    echo "</ul>";
} else {
    echo "<p>⚠️ Cannot test sorting without client data.</p>";
}

// Test 3: Verify sorting function
echo "<h2>3. Sorting Function Verification</h2>";

// Include the function from clients.php
function getClientsWithPolicyData($sortColumn = 'created_at', $sortOrder = 'DESC') {
    global $pdo;
    $clients = [];

    try {
        // Validate sort column to prevent SQL injection
        $allowedColumns = [
            'client_id' => 'c.client_id',
            'name' => 'c.name',
            'ic_number' => 'c.ic_number',
            'gender' => 'c.gender',
            'phone_number' => 'c.phone_number',
            'email' => 'c.email',
            'created_at' => 'c.created_at'
        ];
        
        $orderByColumn = isset($allowedColumns[$sortColumn]) ? $allowedColumns[$sortColumn] : 'c.created_at';
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        // Get all clients with policy counts in a single query
        $query = "SELECT
            c.*,
            COUNT(p.policy_id) as total_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'active' THEN 1 END) as active_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'pending' THEN 1 END) as pending_policies
            FROM clients c
            LEFT JOIN policies p ON c.client_id = p.client_id
            GROUP BY c.client_id, c.name, c.ic_number, c.client_number, c.gender, c.date_of_birth,
                     c.phone_number, c.email, c.address, c.marital_status, c.race, c.religion,
                     c.nationality, c.occupation, c.exact_duties, c.nature_of_business,
                     c.salary_yearly, c.company_name, c.company_address, c.weight, c.height,
                     c.smoker, c.hospital_admission_history, c.status, c.policy_id, c.created_at, c.updated_at
            ORDER BY $orderByColumn $sortOrder";

        $stmt = $pdo->query($query);
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $clients;

    } catch (PDOException $e) {
        error_log("Error in getClientsWithPolicyData: " . $e->getMessage());
        return [];
    }
}

if ($totalClients > 0) {
    try {
        // Test sorting by name
        $clientsByName = getClientsWithPolicyData('name', 'ASC');
        echo "✅ Name sorting test: Found " . count($clientsByName) . " clients<br>";
        
        if (count($clientsByName) >= 2) {
            $first = $clientsByName[0]['name'];
            $second = $clientsByName[1]['name'];
            echo "First two names in alphabetical order: '$first', '$second'<br>";
        }
        
        // Test sorting by client_id
        $clientsById = getClientsWithPolicyData('client_id', 'ASC');
        echo "✅ Client ID sorting test: Found " . count($clientsById) . " clients<br>";
        
    } catch (Exception $e) {
        echo "❌ Error testing sorting function: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Summary and instructions
echo "<h2>4. Sorting Implementation Summary</h2>";

echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Sorting Functionality Added!</h3>";
echo "<p>The client table now supports sorting by:</p>";
echo "<ul>";
echo "<li><strong>Client ID</strong> - Alphanumeric sorting</li>";
echo "<li><strong>Name</strong> - Alphabetical sorting (A-Z or Z-A)</li>";
echo "<li><strong>IC Number</strong> - Numeric/alphanumeric sorting</li>";
echo "<li><strong>Gender</strong> - Alphabetical sorting</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🔍 How to Use Sorting:</h3>";
echo "<ol>";
echo "<li><strong>Visit the clients page:</strong> <a href='clients.php' target='_blank'>Open clients.php</a></li>";
echo "<li><strong>Click on column headers</strong> - Client ID, Name, IC Number, or Gender</li>";
echo "<li><strong>Notice the sort icons:</strong>";
echo "<ul>";
echo "<li><i class='fas fa-sort text-muted'></i> - Column is not currently sorted</li>";
echo "<li><i class='fas fa-sort-up'></i> - Column is sorted ascending (A-Z, 1-9)</li>";
echo "<li><i class='fas fa-sort-down'></i> - Column is sorted descending (Z-A, 9-1)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Click again</strong> to reverse the sort order</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;'>";
echo "<h3>📋 Technical Implementation:</h3>";
echo "<ul>";
echo "<li><strong>URL Parameters:</strong> ?sort=column&order=ASC/DESC</li>";
echo "<li><strong>SQL Injection Protection:</strong> Whitelist of allowed columns</li>";
echo "<li><strong>Visual Indicators:</strong> Font Awesome sort icons</li>";
echo "<li><strong>Hover Effects:</strong> Column headers change color on hover</li>";
echo "<li><strong>Default Sorting:</strong> By creation date (newest first)</li>";
echo "</ul>";
echo "</div>";

if ($totalClients === 0) {
    echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336; margin: 20px 0;'>";
    echo "<h3>⚠️ No Test Data Available</h3>";
    echo "<p>To test the sorting functionality:</p>";
    echo "<ul>";
    echo "<li>Add some clients through your FlutterFlow app</li>";
    echo "<li>Or manually insert test data into the clients table</li>";
    echo "<li>Make sure clients have different names, IDs, IC numbers, and genders</li>";
    echo "</ul>";
    echo "</div>";
}
?>
