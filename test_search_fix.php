<?php
// test_search_fix.php - Test the fixed search functionality
session_start();
require_once 'config.php';

// Set content type to HTML
header('Content-Type: text/html; charset=UTF-8');

// Check if user is logged in (simulate login for testing)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 'test-user-id';
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Search Function Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .test-search { padding: 10px; width: 300px; margin: 10px 0; }
        .test-results { border: 1px solid #ddd; padding: 10px; min-height: 100px; background: #f9f9f9; }
    </style>
</head>
<body>

<h1>Search Function Test & Fix</h1>

<?php
// Test 1: Check database connection
echo "<div class='section'>";
echo "<h2>1. Database Connection Test</h2>";
try {
    if ($pdo) {
        echo "<p class='success'>✅ Database connection successful</p>";
    } else {
        echo "<p class='error'>❌ Database connection failed</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}
echo "</div>";

// Test 2: Check table structure
echo "<div class='section'>";
echo "<h2>2. Table Structure Check</h2>";

// Check agents table
try {
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'agents' ORDER BY ordinal_position");
    $agentColumns = $stmt->fetchAll();
    echo "<h3>Agents Table Columns:</h3>";
    echo "<table><tr><th>Column</th><th>Type</th></tr>";
    foreach ($agentColumns as $col) {
        echo "<tr><td>{$col['column_name']}</td><td>{$col['data_type']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking agents table: " . $e->getMessage() . "</p>";
}

// Check clients table
try {
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'clients' ORDER BY ordinal_position");
    $clientColumns = $stmt->fetchAll();
    echo "<h3>Clients Table Columns:</h3>";
    echo "<table><tr><th>Column</th><th>Type</th></tr>";
    foreach ($clientColumns as $col) {
        echo "<tr><td>{$col['column_name']}</td><td>{$col['data_type']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking clients table: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 3: Check existing data
echo "<div class='section'>";
echo "<h2>3. Existing Data Check</h2>";

// Check agents
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM agents");
    $agentCount = $stmt->fetchColumn();
    echo "<p class='info'>Agents in database: $agentCount</p>";
    
    if ($agentCount > 0) {
        $stmt = $pdo->query("SELECT id, name, phone_number, agent_number FROM agents LIMIT 3");
        $agents = $stmt->fetchAll();
        echo "<h4>Sample Agents:</h4>";
        echo "<table><tr><th>ID</th><th>Name</th><th>Phone</th><th>Agent Number</th></tr>";
        foreach ($agents as $agent) {
            echo "<tr><td>{$agent['id']}</td><td>{$agent['name']}</td><td>{$agent['phone_number']}</td><td>{$agent['agent_number']}</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking agents: " . $e->getMessage() . "</p>";
}

// Check clients
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
    $clientCount = $stmt->fetchColumn();
    echo "<p class='info'>Clients in database: $clientCount</p>";
    
    if ($clientCount > 0) {
        $stmt = $pdo->query("SELECT client_id, name, email, phone_number FROM clients LIMIT 3");
        $clients = $stmt->fetchAll();
        echo "<h4>Sample Clients:</h4>";
        echo "<table><tr><th>Client ID</th><th>Name</th><th>Email</th><th>Phone</th></tr>";
        foreach ($clients as $client) {
            echo "<tr><td>{$client['client_id']}</td><td>{$client['name']}</td><td>{$client['email']}</td><td>{$client['phone_number']}</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking clients: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 4: Test the fixed search queries
echo "<div class='section'>";
echo "<h2>4. Search Query Test</h2>";

$testQueries = ['test', 'agent', 'client', 'a'];

foreach ($testQueries as $query) {
    echo "<h3>Testing search for: '$query'</h3>";
    
    // Test client search
    try {
        $clientSql = "SELECT 
            client_id as id,
            name,
            email,
            phone_number,
            'client' as type
        FROM clients 
        WHERE name LIKE :query 
        OR email LIKE :query 
        OR phone_number LIKE :query
        OR client_id LIKE :query
        LIMIT 5";
        
        $clientStmt = $pdo->prepare($clientSql);
        $clientStmt->execute([':query' => "%$query%"]);
        $clients = $clientStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='info'>Clients found: " . count($clients) . "</p>";
        if (!empty($clients)) {
            echo "<table><tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th></tr>";
            foreach ($clients as $client) {
                echo "<tr><td>{$client['id']}</td><td>{$client['name']}</td><td>{$client['email']}</td><td>{$client['phone_number']}</td></tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Client search error: " . $e->getMessage() . "</p>";
    }
    
    // Test agent search (fixed)
    try {
        $agentSql = "SELECT 
            a.id as id,
            a.name,
            COALESCE(u.email, '') as email,
            a.phone_number,
            'agent' as type
        FROM agents a
        LEFT JOIN users u ON a.user_id = u.id
        WHERE a.name LIKE :query 
        OR u.email LIKE :query 
        OR a.phone_number LIKE :query
        OR a.agent_number LIKE :query
        OR CAST(a.id AS TEXT) LIKE :query
        LIMIT 5";
        
        $agentStmt = $pdo->prepare($agentSql);
        $agentStmt->execute([':query' => "%$query%"]);
        $agents = $agentStmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p class='info'>Agents found: " . count($agents) . "</p>";
        if (!empty($agents)) {
            echo "<table><tr><th>ID</th><th>Name</th><th>Email</th><th>Phone</th></tr>";
            foreach ($agents as $agent) {
                echo "<tr><td>{$agent['id']}</td><td>{$agent['name']}</td><td>{$agent['email']}</td><td>{$agent['phone_number']}</td></tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>❌ Agent search error: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}
echo "</div>";
?>

<!-- Test 5: Live search test -->
<div class="section">
    <h2>5. Live Search Test</h2>
    <p>Test the live search functionality:</p>
    <input type="text" id="test-search" class="test-search" placeholder="Type to search..." autocomplete="off">
    <div id="test-results" class="test-results">Type something to search...</div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var searchInput = document.getElementById('test-search');
    var searchResults = document.getElementById('test-results');
    var searchTimeout;
    
    searchInput.addEventListener('input', function() {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        
        var query = this.value.trim();
        if (query === '') {
            searchResults.innerHTML = 'Type something to search...';
            return;
        }
        
        if (query.length < 2) {
            searchResults.innerHTML = 'Type at least 2 characters...';
            return;
        }
        
        searchTimeout = setTimeout(function() {
            searchResults.innerHTML = 'Searching...';
            
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'live_search.php?query=' + encodeURIComponent(query), true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var data = JSON.parse(xhr.responseText);
                            displayResults(data);
                        } catch (e) {
                            searchResults.innerHTML = '<p class="error">Error parsing JSON: ' + e.message + '</p><pre>' + xhr.responseText + '</pre>';
                        }
                    } else {
                        searchResults.innerHTML = '<p class="error">HTTP Error: ' + xhr.status + '</p><pre>' + xhr.responseText + '</pre>';
                    }
                }
            };
            
            xhr.onerror = function() {
                searchResults.innerHTML = '<p class="error">Network error occurred</p>';
            };
            
            xhr.send();
        }, 300);
    });
    
    function displayResults(data) {
        if (!data || data.length === 0) {
            searchResults.innerHTML = '<p>No results found</p>';
            return;
        }
        
        var html = '<h4>Results (' + data.length + '):</h4><table><tr><th>Type</th><th>Name</th><th>Email</th><th>Phone</th><th>ID</th></tr>';
        data.forEach(function(item) {
            html += '<tr>' +
                '<td>' + item.type + '</td>' +
                '<td>' + (item.name || '') + '</td>' +
                '<td>' + (item.email || '') + '</td>' +
                '<td>' + (item.phone_number || '') + '</td>' +
                '<td>' + (item.id || '') + '</td>' +
                '</tr>';
        });
        html += '</table>';
        
        searchResults.innerHTML = html;
    }
});
</script>

</body>
</html>
