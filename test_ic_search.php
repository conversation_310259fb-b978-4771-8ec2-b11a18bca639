<?php
// test_ic_search.php - Test IC number search functionality

session_start();
require_once 'config.php';

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h1>IC Number Search Test</h1>";

// Test 1: Check if we have clients and agents with IC numbers
echo "<h2>1. Sample Data Check</h2>";

try {
    // Check clients with IC numbers
    $stmt = $pdo->query("SELECT client_id, name, ic_number, phone_number FROM clients WHERE ic_number IS NOT NULL AND ic_number != '' LIMIT 5");
    $clients = $stmt->fetchAll();
    
    echo "<h3>Clients with IC Numbers:</h3>";
    if (count($clients) > 0) {
        echo "<ul>";
        foreach ($clients as $client) {
            echo "<li><strong>{$client['name']}</strong> - IC: {$client['ic_number']}, Phone: {$client['phone_number']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No clients found with IC numbers</p>";
    }
    
    // Check agents with IC numbers
    $stmt = $pdo->query("SELECT id, name, ic_number, phone_number FROM agents WHERE ic_number IS NOT NULL AND ic_number != '' LIMIT 5");
    $agents = $stmt->fetchAll();
    
    echo "<h3>Agents with IC Numbers:</h3>";
    if (count($agents) > 0) {
        echo "<ul>";
        foreach ($agents as $agent) {
            echo "<li><strong>{$agent['name']}</strong> - IC: {$agent['ic_number']}, Phone: {$agent['phone_number']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ No agents found with IC numbers</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error checking data: " . $e->getMessage() . "</p>";
}

// Test 2: Test search functionality
echo "<h2>2. Search Functionality Test</h2>";

if (count($clients) > 0 || count($agents) > 0) {
    // Get a sample IC number to test with
    $testIC = null;
    $testName = null;
    
    if (count($clients) > 0) {
        $testIC = $clients[0]['ic_number'];
        $testName = $clients[0]['name'];
    } elseif (count($agents) > 0) {
        $testIC = $agents[0]['ic_number'];
        $testName = $agents[0]['name'];
    }
    
    if ($testIC) {
        // Test partial IC search
        $partialIC = substr($testIC, 0, 6); // First 6 digits
        
        echo "<h3>Testing Search with Partial IC: '$partialIC'</h3>";
        echo "<p>This should find: <strong>$testName</strong> (IC: $testIC)</p>";
        
        // Simulate the search request
        $_GET['query'] = $partialIC;
        
        echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Search Results:</h4>";
        
        // Include the search logic
        ob_start();
        include 'live_search.php';
        $searchResults = ob_get_clean();
        
        $results = json_decode($searchResults, true);
        
        if ($results && !isset($results['error'])) {
            if (count($results) > 0) {
                echo "✅ Found " . count($results) . " result(s):<br>";
                foreach ($results as $result) {
                    $info = [];
                    if ($result['email']) $info[] = $result['email'];
                    if ($result['phone_number']) $info[] = $result['phone_number'];
                    if ($result['ic_number']) $info[] = 'IC: ' . $result['ic_number'];
                    $infoText = implode(' • ', $info);
                    
                    echo "- <strong>{$result['name']}</strong> ({$result['type']}) - $infoText<br>";
                }
            } else {
                echo "❌ No results found";
            }
        } else {
            echo "❌ Search error: " . ($results['error'] ?? 'Unknown error');
        }
        echo "</div>";
    }
} else {
    echo "<p>⚠️ No test data available. Please add some clients or agents with IC numbers first.</p>";
}

// Test 3: Interactive search test
echo "<h2>3. Interactive Search Test</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<p><strong>✅ Search Enhancement Complete!</strong></p>";
echo "<p>The search bar now supports searching by:</p>";
echo "<ul>";
echo "<li>✅ <strong>Name</strong> - Search by client or agent name</li>";
echo "<li>✅ <strong>Phone Number</strong> - Search by phone number</li>";
echo "<li>✅ <strong>IC Number</strong> - Search by IC number (full or partial)</li>";
echo "<li>✅ <strong>Email</strong> - Search by email address</li>";
echo "<li>✅ <strong>Client ID</strong> - Search by client ID</li>";
echo "<li>✅ <strong>Agent Number</strong> - Search by agent number</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🔍 How to Test:</h3>";
echo "<ol>";
echo "<li>Go to any page with the sidebar (like <a href='clients.php'>clients.php</a> or <a href='agents.php'>agents.php</a>)</li>";
echo "<li>Use the search bar in the sidebar</li>";
echo "<li>Try searching for:</li>";
echo "<ul>";
if (count($clients) > 0) {
    echo "<li>IC number: <code>" . substr($clients[0]['ic_number'], 0, 6) . "</code></li>";
    echo "<li>Name: <code>" . substr($clients[0]['name'], 0, 4) . "</code></li>";
}
if (count($agents) > 0) {
    echo "<li>Agent IC: <code>" . substr($agents[0]['ic_number'], 0, 6) . "</code></li>";
}
echo "</ul>";
echo "<li>Results should show name, email, phone, and IC number</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;'>";
echo "<h3>📋 Technical Details:</h3>";
echo "<p>Updated files:</p>";
echo "<ul>";
echo "<li><code>live_search.php</code> - Added IC number search to SQL queries</li>";
echo "<li><code>layout.php</code> - Enhanced search result display and updated placeholder</li>";
echo "<li><code>debug_search.php</code> - Updated debug output to show IC numbers</li>";
echo "</ul>";
echo "</div>";
?>
