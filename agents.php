<?php
// agents.php
session_start();
require_once 'config.php';

// Add this line temporarily to verify config.php is loaded
if (!function_exists('getAllAgentsSorted')) {
    die('Config file not loaded properly');
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Set page title
$pageTitle = 'AGENT LIST';

// Handle agent deletion if requested
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $agentId = $_GET['delete'];
    if (deleteAgent($agentId)) {
        header('Location: agents.php?success=Agent deleted successfully');
        exit;
    } else {
        header('Location: agents.php?error=Failed to delete agent');
        exit;
    }
}

// Handle sorting
$sortColumn = isset($_GET['sort']) ? $_GET['sort'] : 'agent_number';
$sortOrder = isset($_GET['order']) ? $_GET['order'] : 'asc';

// Validate sort column to prevent SQL injection
$allowedColumns = ['name', 'agent_number', 'gender'];
if (!in_array($sortColumn, $allowedColumns)) {
    $sortColumn = 'agent_number';
}

// Get all agents from database with sorting
$agents = getAllAgentsSorted($sortColumn, $sortOrder);

// Include layout header
include 'layout.php';
?>

<!--<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-dark font-weight-bold">Agents</h2>
    <a href="agent_add.php" class="btn btn-success">
        <i class="fas fa-plus mr-2"></i> Add New Member
    </a>
</div>-->

<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr class="bg-light">
                        <th class="px-4 py-3">
                            <a href="?sort=agent_number&order=<?php echo $sortColumn === 'agent_number' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>" class="text-dark text-decoration-none">
                                Agent Number
                                <?php if ($sortColumn === 'agent_number'): ?>
                                    <i class="fas fa-sort-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php else: ?>
                                    <i class="fas fa-sort"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th class="px-4 py-3">
                            <a href="?sort=name&order=<?php echo $sortColumn === 'name' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>" class="text-dark text-decoration-none">
                                Name
                                <?php if ($sortColumn === 'name'): ?>
                                    <i class="fas fa-sort-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php else: ?>
                                    <i class="fas fa-sort"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th class="px-4 py-3">IC Number</th>
                        <th class="px-4 py-3">
                            <a href="?sort=gender&order=<?php echo $sortColumn === 'gender' && $sortOrder === 'asc' ? 'desc' : 'asc'; ?>" class="text-dark text-decoration-none">
                                Gender
                                <?php if ($sortColumn === 'gender'): ?>
                                    <i class="fas fa-sort-<?php echo $sortOrder === 'asc' ? 'up' : 'down'; ?>"></i>
                                <?php else: ?>
                                    <i class="fas fa-sort"></i>
                                <?php endif; ?>
                            </a>
                        </th>
                        <th class="px-4 py-3">Phone</th>
                        <th class="px-4 py-3">Email</th>
                        <th class="px-4 py-3 text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($agents)): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">No agents found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($agents as $agent): ?>
                            <tr>
                                <td class="px-4 py-3">
                                    <?php if (!empty($agent['agent_number'])): ?>
                                        <?php echo htmlspecialchars($agent['agent_number']); ?>
                                    <?php else: ?>
                                        <span style="color: #e74c3c; font-style: italic;">Not Assigned</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($agent['name']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($agent['ic_number']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($agent['gender']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($agent['phone_number']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($agent['email']); ?></td>
                                <td class="px-4 py-3 text-center" style="white-space: nowrap;">
                                    <a href="agent_view.php?id=<?php echo htmlspecialchars($agent['id']); ?>" class="btn btn-sm btn-info px-3">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
    .table {
        font-size: 0.95rem;
    }
    .table thead tr {
        border-bottom: 2px solid #e0e0e0;
    }
    .table td, .table th {
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }
    th a {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    th a:hover {
        text-decoration: none;
        opacity: 0.8;
    }
    .fa-sort {
        color: #999;
    }
    .fa-sort-up, .fa-sort-down {
        color: #333;
    }
</style>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="agentName"></strong>?</p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle mr-2"></i> 
                    <strong>Warning:</strong> This will permanently remove all agent data from the system.
                </div>
                <p class="text-danger mt-2">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                    <i class="fas fa-trash mr-1"></i> Delete
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(agentId, agentName) {
    document.getElementById('agentName').textContent = agentName;
    document.getElementById('confirmDeleteBtn').href = 'agents.php?delete=' + encodeURIComponent(agentId);
    $('#deleteModal').modal('show');
}
</script>

<?php include 'layout_footer.php'; ?>