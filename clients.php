<?php
session_start();
require_once 'config.php';

// Set page title
$pageTitle = 'CLIENT LIST';

// Use a different function name to avoid conflicts
function getClientsWithPolicyData($sortColumn = 'created_at', $sortOrder = 'DESC') {
    global $pdo;
    $clients = [];

    try {
        // Validate sort column to prevent SQL injection
        $allowedColumns = [
            'client_id' => 'c.client_id',
            'name' => 'c.name',
            'ic_number' => 'c.ic_number',
            'gender' => 'c.gender',
            'phone_number' => 'c.phone_number',
            'email' => 'c.email',
            'created_at' => 'c.created_at'
        ];

        $orderByColumn = isset($allowedColumns[$sortColumn]) ? $allowedColumns[$sortColumn] : 'c.created_at';
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        // Get all clients with policy counts in a single query
        $query = "SELECT
            c.*,
            COUNT(p.policy_id) as total_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'active' THEN 1 END) as active_policies,
            COUNT(CASE WHEN LOWER(TRIM(p.status)) = 'pending' THEN 1 END) as pending_policies
            FROM clients c
            LEFT JOIN policies p ON c.client_id = p.client_id
            GROUP BY c.client_id, c.name, c.ic_number, c.client_number, c.gender, c.date_of_birth,
                     c.phone_number, c.email, c.address, c.marital_status, c.race, c.religion,
                     c.nationality, c.occupation, c.exact_duties, c.nature_of_business,
                     c.salary_yearly, c.company_name, c.company_address, c.weight, c.height,
                     c.smoker, c.hospital_admission_history, c.status, c.policy_id, c.created_at, c.updated_at
            ORDER BY $orderByColumn $sortOrder";

        $stmt = $pdo->query($query);
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return $clients;

    } catch (PDOException $e) {
        error_log("Error in getClientsWithPolicyData: " . $e->getMessage());
        return [];
    }
}

// Get sorting parameters from URL
$sortColumn = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$sortOrder = isset($_GET['order']) ? $_GET['order'] : 'DESC';

// Function to generate sortable column header
function getSortableHeader($column, $title, $currentSort, $currentOrder) {
    $newOrder = ($currentSort === $column && $currentOrder === 'ASC') ? 'DESC' : 'ASC';
    $url = '?sort=' . $column . '&order=' . $newOrder;

    $icon = '';
    if ($currentSort === $column) {
        $icon = $currentOrder === 'ASC' ? ' <i class="fas fa-sort-up"></i>' : ' <i class="fas fa-sort-down"></i>';
    } else {
        $icon = ' <i class="fas fa-sort text-muted"></i>';
    }

    return '<a href="' . $url . '" class="text-decoration-none text-dark">' . $title . $icon . '</a>';
}

// Get all clients with sorting
$clients = getClientsWithPolicyData($sortColumn, $sortOrder);

// Include the layout header
include 'layout.php';

// Display any error messages
if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            ' . htmlspecialchars($_GET['error']) . '
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
          </div>';
}
?>

<!--<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-dark font-weight-bold">Clients</h2>
    <a href="client_add.php" class="btn btn-success">
        <i class="fas fa-plus mr-2"></i> Add New Client
    </a>
</div>-->

<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr class="bg-light">
                        <th class="px-4 py-3"><?php echo getSortableHeader('client_id', 'Client ID', $sortColumn, $sortOrder); ?></th>
                        <th class="px-4 py-3"><?php echo getSortableHeader('name', 'Name', $sortColumn, $sortOrder); ?></th>
                        <th class="px-4 py-3"><?php echo getSortableHeader('ic_number', 'IC Number', $sortColumn, $sortOrder); ?></th>
                        <th class="px-4 py-3"><?php echo getSortableHeader('gender', 'Gender', $sortColumn, $sortOrder); ?></th>
                        <th class="px-4 py-3">Phone</th>
                        <th class="px-4 py-3">Email</th>
                        <th class="px-4 py-3 text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                        <tr>
                            <td colspan="7" class="text-center py-4">No clients found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                            <tr data-client-id="<?php echo $client['client_id']; ?>">
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['client_id']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['name']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['ic_number'] ?? ''); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['gender'] ?? ''); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['phone_number']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['email']); ?></td>
                                <td class="px-4 py-3 text-center">
                                    <a href="client_view.php?id=<?php echo $client['client_id']; ?>" class="btn btn-sm btn-info mr-1" title="View Client">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                   
                                </td>
                            </tr>
                        <?php endforeach; ?>

                    <?php endif; ?>
                </tbody>
            </table>
        </div>  
    </div>
</div>



<style>
    .table {
        font-size: 0.95rem;
    }
    .table thead tr {
        border-bottom: 2px solid #e0e0e0;
    }
    .table td, .table th {
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .action-btn {
        width: 48px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: none;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
        opacity: 0.9;
    }

    .action-btn i {
        font-size: 13px;
    }

    .action-btn.view {
        background-color: #17a2b8;
    }

    .action-btn.edit {
        background-color: #6c757d;
    }

    .action-btn.delete {
        background-color: #dc3545;
    }

    /* Status badge styling */
    .badge {
        font-size: 0.85rem;
        padding: 0.4em 0.8em;
        font-weight: 500;
        border-radius: 4px;
    }

    .badge-success {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .badge-secondary {
        background-color: #f5f5f5;
        color: #616161;
    }

    .badge-warning {
        background-color: #fff3e0;
        color: #e65100;
    }

    .badge-info {
        background-color: #e3f2fd;
        color: #0d47a1;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }
    .modal-content {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .modal-header {
        border-bottom: none;
        padding: 20px 25px;
    }
    .modal-title {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
    }
    .modal-body {
        padding: 20px 25px;
    }
    .modal-body p {
        margin-bottom: 10px;
        font-size: 16px;
    }
    .modal-body .text-danger {
        color: #dc3545;
        font-size: 14px;
    }
    .modal-footer {
        border-top: none;
        padding: 15px 25px 20px;
    }
    .btn-secondary {
        background-color: #6c757d;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .btn-danger {
        background-color: #dc3545;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .close {
        font-size: 1.5rem;
        opacity: 0.5;
        transition: opacity 0.2s;
    }
    .close:hover {
        opacity: 1;
    }

    /* New outline badge styles */
    .policy-details {
        margin-top: 5px;
    }
    
    .badge-outline-success {
        background-color: transparent;
        border: 1px solid #28a745;
        color: #28a745;
        font-size: 0.75rem;
    }
    
    .badge-outline-warning {
        background-color: transparent;
        border: 1px solid #ffc107;
        color: #e65100;
        font-size: 0.75rem;
    }
    
    .badge-outline-info {
        background-color: transparent;
        border: 1px solid #17a2b8;
        color: #0d47a1;
        font-size: 0.75rem;
    }
    
    .mt-1 {
        margin-top: 0.25rem;
    }

    /* Sortable header styles */
    .table thead th a {
        color: #495057 !important;
        text-decoration: none !important;
        display: block;
        padding: 0;
    }

    .table thead th a:hover {
        color: #007bff !important;
    }

    .table thead th a i {
        font-size: 0.8em;
        margin-left: 5px;
    }

    .table thead th {
        cursor: pointer;
        user-select: none;
    }
</style>

<!-- JavaScript for delete confirmation -->
<script>
function confirmDelete(clientId, clientName) {
    if (confirm('Are you sure you want to delete client: ' + clientName + '? This action cannot be undone.')) {
        deleteClient(clientId);
    }
}

function deleteClient(clientId) {
    fetch('delete_client.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            client_id: clientId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show updated list
            window.location.reload();
        } else {
            alert('Error deleting client: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the client');
    });
}
</script>

<?php include 'layout_footer.php'; ?>