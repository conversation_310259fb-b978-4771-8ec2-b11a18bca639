<?php
// test_client_table.php - Test the updated client table structure

session_start();
require_once 'config.php';

// Simulate admin login for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';

echo "<h1>Client Table Structure Test</h1>";

// Test 1: Check client data structure
echo "<h2>1. Client Data Structure Check</h2>";

try {
    // Get sample clients to check data structure
    $stmt = $pdo->query("SELECT client_id, name, ic_number, gender, phone_number, email FROM clients LIMIT 5");
    $clients = $stmt->fetchAll();
    
    if (count($clients) > 0) {
        echo "✅ Found " . count($clients) . " clients<br>";
        echo "<h3>Sample Client Data:</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>Client ID</th><th>Name</th><th>IC Number</th><th>Gender</th><th>Phone</th><th>Email</th>";
        echo "</tr>";
        
        foreach ($clients as $client) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($client['client_id']) . "</td>";
            echo "<td>" . htmlspecialchars($client['name']) . "</td>";
            echo "<td>" . htmlspecialchars($client['ic_number'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($client['gender'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($client['phone_number']) . "</td>";
            echo "<td>" . htmlspecialchars($client['email']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No clients found in database<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking client data: " . $e->getMessage() . "<br>";
}

// Test 2: Check column availability
echo "<h2>2. Database Column Check</h2>";

try {
    // Check if all required columns exist
    $stmt = $pdo->query("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'clients' AND column_name IN ('client_id', 'name', 'ic_number', 'gender', 'phone_number', 'email') ORDER BY column_name");
    $columns = $stmt->fetchAll();
    
    $requiredColumns = ['client_id', 'name', 'ic_number', 'gender', 'phone_number', 'email'];
    $foundColumns = array_column($columns, 'column_name');
    
    echo "<h3>Required Columns Status:</h3>";
    foreach ($requiredColumns as $col) {
        $status = in_array($col, $foundColumns) ? '✅' : '❌';
        echo "$status <strong>$col</strong><br>";
    }
    
    if (count($foundColumns) === count($requiredColumns)) {
        echo "<p style='color: green;'>✅ All required columns are available!</p>";
    } else {
        echo "<p style='color: red;'>❌ Some columns are missing!</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking columns: " . $e->getMessage() . "<br>";
}

// Test 3: Data quality check
echo "<h2>3. Data Quality Check</h2>";

try {
    // Check data completeness
    $stmt = $pdo->query("SELECT 
        COUNT(*) as total_clients,
        COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as clients_with_name,
        COUNT(CASE WHEN ic_number IS NOT NULL AND ic_number != '' THEN 1 END) as clients_with_ic,
        COUNT(CASE WHEN gender IS NOT NULL AND gender != '' THEN 1 END) as clients_with_gender,
        COUNT(CASE WHEN phone_number IS NOT NULL AND phone_number != '' THEN 1 END) as clients_with_phone,
        COUNT(CASE WHEN email IS NOT NULL AND email != '' THEN 1 END) as clients_with_email
        FROM clients");
    $stats = $stmt->fetch();
    
    if ($stats['total_clients'] > 0) {
        echo "<h3>Data Completeness:</h3>";
        echo "<ul>";
        echo "<li><strong>Total Clients:</strong> " . $stats['total_clients'] . "</li>";
        echo "<li><strong>With Name:</strong> " . $stats['clients_with_name'] . " (" . round(($stats['clients_with_name']/$stats['total_clients'])*100, 1) . "%)</li>";
        echo "<li><strong>With IC Number:</strong> " . $stats['clients_with_ic'] . " (" . round(($stats['clients_with_ic']/$stats['total_clients'])*100, 1) . "%)</li>";
        echo "<li><strong>With Gender:</strong> " . $stats['clients_with_gender'] . " (" . round(($stats['clients_with_gender']/$stats['total_clients'])*100, 1) . "%)</li>";
        echo "<li><strong>With Phone:</strong> " . $stats['clients_with_phone'] . " (" . round(($stats['clients_with_phone']/$stats['total_clients'])*100, 1) . "%)</li>";
        echo "<li><strong>With Email:</strong> " . $stats['clients_with_email'] . " (" . round(($stats['clients_with_email']/$stats['total_clients'])*100, 1) . "%)</li>";
        echo "</ul>";
    } else {
        echo "<p>No clients found for data quality analysis.</p>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking data quality: " . $e->getMessage() . "<br>";
}

// Test 4: Table display preview
echo "<h2>4. Updated Table Structure</h2>";

echo "<div style='background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 20px 0;'>";
echo "<h3>✅ Client Table Updated Successfully!</h3>";
echo "<p>The client table now displays the following columns:</p>";
echo "<ol>";
echo "<li><strong>Client ID</strong> - Unique identifier for each client</li>";
echo "<li><strong>Name</strong> - Client's full name</li>";
echo "<li><strong>IC Number</strong> - Identity card number</li>";
echo "<li><strong>Gender</strong> - Client's gender</li>";
echo "<li><strong>Phone</strong> - Contact phone number</li>";
echo "<li><strong>Email</strong> - Email address</li>";
echo "<li><strong>Actions</strong> - View button for detailed client information</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 20px 0;'>";
echo "<h3>🔍 How to Test:</h3>";
echo "<ol>";
echo "<li><strong>Visit the clients page:</strong> <a href='clients.php' target='_blank'>Open clients.php</a></li>";
echo "<li><strong>Check the table headers</strong> - Should show: Client ID, Name, IC Number, Gender, Phone, Email, Actions</li>";
echo "<li><strong>Verify data display</strong> - Each row should show the corresponding client information</li>";
echo "<li><strong>Test the View button</strong> - Should link to client_view.php with the correct client ID</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e3f2fd; padding: 15px; border-left: 4px solid #2196f3; margin: 20px 0;'>";
echo "<h3>📋 Changes Made:</h3>";
echo "<p><strong>File Updated:</strong> <code>clients.php</code></p>";
echo "<p><strong>Changes:</strong></p>";
echo "<ul>";
echo "<li>Updated table headers to show: Client ID, Name, IC Number, Gender, Phone, Email, Actions</li>";
echo "<li>Modified table rows to display the new column data</li>";
echo "<li>Removed old status column with policy information</li>";
echo "<li>Updated colspan for 'No clients found' message</li>";
echo "<li>Added proper null handling for IC Number and Gender fields</li>";
echo "</ul>";
echo "</div>";

if (count($clients ?? []) === 0) {
    echo "<div style='background: #ffebee; padding: 15px; border-left: 4px solid #f44336; margin: 20px 0;'>";
    echo "<h3>⚠️ No Test Data Available</h3>";
    echo "<p>To see the table in action, you need to:</p>";
    echo "<ul>";
    echo "<li>Add some clients through your FlutterFlow app</li>";
    echo "<li>Or manually insert test data into the clients table</li>";
    echo "<li>Make sure the clients have IC numbers and gender information</li>";
    echo "</ul>";
    echo "</div>";
}
?>
