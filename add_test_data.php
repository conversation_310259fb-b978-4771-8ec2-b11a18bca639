<?php
// add_test_data.php - Add sample data for testing search functionality
session_start();
require_once 'config.php';

// Set content type to HTML
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Add Test Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>

<h1>Add Test Data for Search Testing</h1>

<?php
// Check if we should add test data
$addData = isset($_GET['add']) && $_GET['add'] === 'yes';

if (!$addData) {
    echo "<div class='section'>";
    echo "<h2>Current Data Status</h2>";
    
    // Check current data
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM clients");
        $clientCount = $stmt->fetchColumn();
        echo "<p class='info'>Clients in database: $clientCount</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM agents");
        $agentCount = $stmt->fetchColumn();
        echo "<p class='info'>Agents in database: $agentCount</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        echo "<p class='info'>Users in database: $userCount</p>";
        
        if ($clientCount == 0 || $agentCount == 0) {
            echo "<p class='info'>You need test data to test the search functionality.</p>";
            echo "<p><a href='?add=yes' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Add Test Data</a></p>";
        } else {
            echo "<p class='success'>You have existing data. You can test the search functionality.</p>";
            echo "<p><a href='test_search_fix.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Search Function</a></p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>Error checking data: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
} else {
    echo "<div class='section'>";
    echo "<h2>Adding Test Data...</h2>";
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Add test users first
        echo "<h3>Adding Test Users</h3>";
        $testUsers = [
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>'],
            ['email' => '<EMAIL>']
        ];
        
        $userIds = [];
        foreach ($testUsers as $user) {
            $stmt = $pdo->prepare("INSERT INTO users (email, created_at) VALUES (?, NOW()) ON CONFLICT (email) DO NOTHING RETURNING id");
            $stmt->execute([$user['email']]);
            $result = $stmt->fetch();
            if ($result) {
                $userIds[] = $result['id'];
                echo "<p class='success'>✅ Added user: {$user['email']}</p>";
            } else {
                // User already exists, get the ID
                $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
                $stmt->execute([$user['email']]);
                $result = $stmt->fetch();
                if ($result) {
                    $userIds[] = $result['id'];
                    echo "<p class='info'>ℹ️ User already exists: {$user['email']}</p>";
                }
            }
        }
        
        // Add test agents
        echo "<h3>Adding Test Agents</h3>";
        $testAgents = [
            [
                'name' => 'John Smith',
                'ic_number' => '123456789012',
                'gender' => 'Male',
                'date_of_birth' => '1985-05-15',
                'phone_number' => '0123456789',
                'address' => '123 Main Street, Kuala Lumpur',
                'agent_number' => 'AGT001',
                'status' => 'Active',
                'user_id' => $userIds[0] ?? null
            ],
            [
                'name' => 'Sarah Johnson',
                'ic_number' => '987654321098',
                'gender' => 'Female',
                'date_of_birth' => '1990-08-22',
                'phone_number' => '0198765432',
                'address' => '456 Oak Avenue, Petaling Jaya',
                'agent_number' => 'AGT002',
                'status' => 'Active',
                'user_id' => $userIds[1] ?? null
            ],
            [
                'name' => 'Michael Chen',
                'ic_number' => '555666777888',
                'gender' => 'Male',
                'date_of_birth' => '1988-12-03',
                'phone_number' => '0155566677',
                'address' => '789 Pine Road, Shah Alam',
                'agent_number' => 'AGT003',
                'status' => 'Active',
                'user_id' => $userIds[2] ?? null
            ]
        ];
        
        foreach ($testAgents as $agent) {
            $stmt = $pdo->prepare("INSERT INTO agents (name, ic_number, gender, date_of_birth, phone_number, address, agent_number, status, user_id, created_at, updated_at) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([
                $agent['name'],
                $agent['ic_number'],
                $agent['gender'],
                $agent['date_of_birth'],
                $agent['phone_number'],
                $agent['address'],
                $agent['agent_number'],
                $agent['status'],
                $agent['user_id']
            ]);
            echo "<p class='success'>✅ Added agent: {$agent['name']}</p>";
        }
        
        // Add test clients
        echo "<h3>Adding Test Clients</h3>";
        $testClients = [
            [
                'client_id' => 'CLI001',
                'name' => 'Alice Wong',
                'ic_number' => '111222333444',
                'gender' => 'Female',
                'date_of_birth' => '1992-03-10',
                'phone_number' => '0111222333',
                'email' => '<EMAIL>',
                'address' => '321 Elm Street, Subang Jaya',
                'status' => 'Active'
            ],
            [
                'client_id' => 'CLI002',
                'name' => 'Robert Tan',
                'ic_number' => '444555666777',
                'gender' => 'Male',
                'date_of_birth' => '1987-07-18',
                'phone_number' => '0144455566',
                'email' => '<EMAIL>',
                'address' => '654 Maple Drive, Damansara',
                'status' => 'Active'
            ],
            [
                'client_id' => 'CLI003',
                'name' => 'Lisa Kumar',
                'ic_number' => '777888999000',
                'gender' => 'Female',
                'date_of_birth' => '1995-11-25',
                'phone_number' => '0177788899',
                'email' => '<EMAIL>',
                'address' => '987 Cedar Lane, Mont Kiara',
                'status' => 'Active'
            ]
        ];
        
        foreach ($testClients as $client) {
            $stmt = $pdo->prepare("INSERT INTO clients (client_id, name, ic_number, gender, date_of_birth, phone_number, email, address, status, created_at, updated_at) 
                                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([
                $client['client_id'],
                $client['name'],
                $client['ic_number'],
                $client['gender'],
                $client['date_of_birth'],
                $client['phone_number'],
                $client['email'],
                $client['address'],
                $client['status']
            ]);
            echo "<p class='success'>✅ Added client: {$client['name']}</p>";
        }
        
        // Commit transaction
        $pdo->commit();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Test Data Added Successfully!</h3>";
        echo "<p style='color: #155724;'>You can now test the search functionality with the sample data.</p>";
        echo "<p><a href='test_search_fix.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Search Function</a></p>";
        echo "</div>";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollback();
        echo "<p class='error'>❌ Error adding test data: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}
?>

</body>
</html>
