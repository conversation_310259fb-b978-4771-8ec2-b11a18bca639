<?php
// agent_view.php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if agent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: agents.php?error=Invalid agent ID');
    exit;
}

$agentId = $_GET['id'];
$agent = getAgentById($agentId);

if (!$agent) {
    header('Location: agents.php?error=Agent not found');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'assign_agent_number') {
            // Assign agent number
            $newAgentNumber = trim($_POST['agent_number']);
            $id = $_POST['id'];

            if (!empty($newAgentNumber)) {
                try {
                    $stmt = $pdo->prepare("UPDATE agents SET agent_number = ?, updated_at = NOW() WHERE id = ?");
                    if ($stmt->execute([$newAgentNumber, $id])) {
                        header("Location: agent_view.php?id=$id&success=Agent Number assigned successfully");
                        exit;
                    } else {
                        $error = "Failed to assign agent number";
                    }
                } catch (PDOException $e) {
                    if (strpos($e->getMessage(), 'duplicate key') !== false) {
                        $error = "Agent Number already exists. Please choose a different number.";
                    } else {
                        $error = "Database error: " . $e->getMessage();
                    }
                }
            } else {
                $error = "Agent Number cannot be empty";
            }
        }
    }
}

// Get agent's education details
$education = getAgentEducation($agentId);

// Get agent's documents
$documents = getAgentDocuments($agentId);

// Set page title
$pageTitle = 'AGENT DETAILS';

// Include layout header
include 'layout.php';
?>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.agent-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.agent-info {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.agent-photo {
    width: 150px;  /* Passport photo width */
    height: 200px; /* Passport photo height */
    background-color: #e9ecef;
    flex-shrink: 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

.agent-photo i {
    font-size: 48px;
    color: #adb5bd;
}

.agent-photo img {
    transition: all 0.3s ease;
}

.agent-photo img:hover {
    opacity: 0.9;
    transform: scale(1.02);
}

.agent-photo img[onclick] {
    cursor: pointer;
}

.agent-photo img[onclick]:hover::after {
    content: "🔍";
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* Loading state for images */
.agent-photo .loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #6c757d;
    font-size: 14px;
}

.agent-details {
    flex-grow: 1;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
}

.detail-value {
    flex-grow: 1;
    color: #2c3e50;
}

.section-container {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.education-table, .client-table {
    width: 100%;
    margin-top: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.education-table th, .client-table th {
    background-color: #2c3e50;
    color: white;
    padding: 15px;
    font-weight: 500;
}

.education-table td, .client-table td {
    padding: 12px 15px;
    background-color: white;
    border-bottom: 1px solid #eee;
}

.education-table tr:last-child td {
    border-bottom: none;
}

.document-section {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.document-link {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 10px;
    background: white;
    border-radius: 8px;
    color: #0066cc;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.document-link:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.document-link i {
    margin-right: 10px;
    color: #e74c3c;
}
</style>

<div class="detail-container">
    <div class="agent-header">
        AGENT DETAILS
                </div>
                
    <div class="agent-info">
        <div class="agent-photo" id="agentPhotoContainer">
            <?php if (!empty($agent['photo'])): ?>
                <div class="loading" id="photoLoading">
                    <i class="fas fa-spinner fa-spin"></i><br>
                    <small>Loading...</small>
                </div>
                <img src="<?php echo htmlspecialchars($agent['photo']); ?>"
                     alt="Agent Photo"
                     style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px; display: none; cursor: pointer;"
                     onload="showPhoto(this)"
                     onerror="showPlaceholder()"
                     onclick="viewFullPhoto('<?php echo htmlspecialchars($agent['photo']); ?>')"
                     title="Click to view full size">
                <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center;" id="photoPlaceholder">
                    <i class="fas fa-user" style="font-size: 48px; color: #adb5bd;"></i>
                </div>
            <?php else: ?>
                <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-user"></i>
                </div>
            <?php endif; ?>
        </div>
                
        <div class="agent-details">
            <div class="detail-row">
                <div class="detail-label">Name :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['name']); ?></div>
                </div>
            <div class="detail-row">
                <div class="detail-label">IC Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['ic_number']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Agent Number :</div>
                <div class="detail-value" style="display: flex; align-items: center; gap: 10px;">
                    <?php if (!empty($agent['agent_number'])): ?>
                        <span><?php echo htmlspecialchars($agent['agent_number']); ?></span>
                        <button type="button" onclick="editAgentNumber()" class="btn btn-sm btn-secondary">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                    <?php else: ?>
                        <form method="POST" style="display: flex; align-items: center; gap: 10px; margin: 0;">
                            <input type="text" name="agent_number" placeholder="Enter Agent Number"
                                   style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; width: 150px;" required>
                            <input type="hidden" name="action" value="assign_agent_number">
                            <input type="hidden" name="id" value="<?php echo $agent['id']; ?>">
                            <button type="submit" class="btn btn-sm btn-success">
                                <i class="fas fa-save"></i> Save
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
        </div>
            <div class="detail-row">
                <div class="detail-label">Gender :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['gender']); ?></div>
    </div>
            <div class="detail-row">
                <div class="detail-label">Date Of Birth :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['date_of_birth']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['phone_number']); ?></div>
                    </div>
            <div class="detail-row">
                <div class="detail-label">Email :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['email']); ?></div>
                </div>
            <div class="detail-row">
                <div class="detail-label">Address :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['address']); ?></div>
                    </div>
            <div class="detail-row">
                <div class="detail-label">Beneficiary's Phone :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['beneficiary_phone']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Status :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['status'] ?? 'Not Assigned'); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Created Date :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['created_at'] ?? ''); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Contact Name :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['contact_name'] ?? 'Not provided'); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Contact Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['contact_num'] ?? 'Not provided'); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Contact IC :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['contact_ic'] ?? 'Not provided'); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Work Experience :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['work_experience']); ?></div>
            </div>
            </div>
        </div>
        
    <!--<div class="section-container">
        <div class="section-title">Education Details</div>
        <table class="education-table">
            <thead>
                <tr>
                    <th>Level</th>
                    <th>Year Completed</th>
                    <th>Institution Name</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($education)): ?>
                    <tr>
                        <td colspan="3" style="text-align: center;">No education details available</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($education as $edu): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($edu['level']); ?></td>
                            <td><?php echo htmlspecialchars($edu['year_completed']); ?></td>
                            <td><?php echo htmlspecialchars($edu['institution_name']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>-->

    <div class="section-container">
        <div class="section-title">Client List</div>
        <table class="client-table">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Name</th>
                    <th>Client ID</th>
                    <th>Policy Number</th>
                                </tr>
            </thead>
            <tbody>
                <?php
                $clients = getAgentClients($agentId);
                if (!empty($clients)):
                    foreach ($clients as $index => $client):
                ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($client['name']); ?></td>
                        <td><?php echo htmlspecialchars($client['client_id']); ?></td>
                        <td><?php echo htmlspecialchars($client['policy_number']); ?></td>
                    </tr>
                <?php
                    endforeach;
                endif;
                ?>
            </tbody>
        </table>
    </div>

    <!--<div class="document-section">
        <div class="section-title">Documents</div>
        <?php if (empty($documents)): ?>
            <p>No documents uploaded.</p>
        <?php else: ?>
            <?php foreach ($documents as $document): ?>
                <a href="<?php echo htmlspecialchars($document['file_path']); ?>" class="document-link" target="_blank">
                    <i class="fas fa-file-pdf doc-icon"></i>
                    <?php echo htmlspecialchars($document['type_name']); ?>: <?php echo htmlspecialchars($document['file_name']); ?>
                </a>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>-->

    <!-- Delete Button Section -->
    <div style="margin-top: 40px; padding: 20px; text-align: center; border-top: 2px solid #e9ecef;">
        <button type="button" onclick="confirmDelete()" class="btn btn-danger" style="padding: 10px 30px; font-size: 16px;">
            <i class="fas fa-trash"></i> Delete Agent
        </button>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" style="position: fixed; top: 20px; right: 20px; z-index: 1050;">
        <?php echo htmlspecialchars($_GET['success']); ?>
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
<?php endif; ?>

<?php if (isset($error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" style="position: fixed; top: 20px; right: 20px; z-index: 1050;">
        <?php echo htmlspecialchars($error); ?>
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
<?php endif; ?>

<script>
function editAgentNumber() {
    // Convert the agent number display to an editable form
    const agentNumberRow = document.querySelector('.detail-value');
    const currentAgentNumber = '<?php echo htmlspecialchars($agent['agent_number'] ?? ''); ?>';

    agentNumberRow.innerHTML = `
        <form method="POST" style="display: flex; align-items: center; gap: 10px; margin: 0;">
            <input type="text" name="agent_number" value="${currentAgentNumber}"
                   style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; width: 150px;" required>
            <input type="hidden" name="action" value="assign_agent_number">
            <input type="hidden" name="id" value="<?php echo $agent['id']; ?>">
            <button type="submit" class="btn btn-sm btn-success">
                <i class="fas fa-save"></i> Save
            </button>
            <button type="button" onclick="cancelEdit()" class="btn btn-sm btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </button>
        </form>
    `;
}

function cancelEdit() {
    location.reload();
}

function confirmDelete() {
    const agentName = '<?php echo addslashes($agent['name'] ?? 'this agent'); ?>';
    const agentId = '<?php echo $agent['id']; ?>';

    if (confirm(`Are you sure you want to delete ${agentName}? This action cannot be undone.`)) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'agents.php';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'delete';
        input.value = agentId;

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

// Photo loading functions
function showPhoto(img) {
    const loading = document.getElementById('photoLoading');
    if (loading) loading.style.display = 'none';
    img.style.display = 'block';
}

function showPlaceholder() {
    const loading = document.getElementById('photoLoading');
    const placeholder = document.getElementById('photoPlaceholder');
    if (loading) loading.style.display = 'none';
    if (placeholder) placeholder.style.display = 'flex';
}

// Function to view full size photo
function viewFullPhoto(photoUrl) {
    if (!photoUrl) return;

    // Open the photo in a new tab/window
    window.open(photoUrl, '_blank');
}

// Auto-hide alerts after 5 seconds
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        alert.style.display = 'none';
    });
}, 5000);
</script>

<?php include 'layout_footer.php'; ?>